import {
  generateCameraId,
  parseCameraId,
  normalizeCollectionName,
  denormalizeCollectionName,
  normalizeCameraId,
  parseStreamUrl
} from '../../utils/cameraUtils';

describe('cameraUtils', () => {
  describe('generateCameraId', () => {
    it('should generate a valid camera ID from collection name and IP', () => {
      const result = generateCameraId('Test Collection', '*************');
      expect(result).toBe('camera-test-collection-192-168-1-100');
    });

    it('should handle special characters in collection name', () => {
      const result = generateCameraId('Test & Collection!', '*************');
      expect(result).toBe('camera-test-collection-192-168-1-100');
    });
  });

  describe('parseCameraId', () => {
    it('should parse a valid camera ID into collection name and IP', () => {
      const result = parseCameraId('camera-test-collection-192-168-1-100');
      expect(result).toEqual({
        collectionName: 'Test Collection',
        ip: '*************'
      });
    });

    it('should handle single word collection names', () => {
      const result = parseCameraId('camera-k1-172-16-1-102');
      expect(result).toEqual({
        collectionName: 'K1',
        ip: '************'
      });
    });

    it('should handle collection names with multiple words', () => {
      const result = parseCameraId('camera-my-test-collection-192-168-1-100');
      expect(result).toEqual({
        collectionName: 'My Test Collection',
        ip: '*************'
      });
    });

    it('should handle invalid camera IDs gracefully', () => {
      const result = parseCameraId('invalid-id');
      expect(result).toEqual({
        collectionName: '',
        ip: ''
      });
    });

    it('should handle camera IDs with insufficient parts', () => {
      const result = parseCameraId('camera-test-192');
      expect(result).toEqual({
        collectionName: '',
        ip: ''
      });
    });

    it('should handle Class A private IP range (10.0.0.0/8)', () => {
      const result = parseCameraId('camera-office-10-0-0-1');
      expect(result).toEqual({
        collectionName: 'Office',
        ip: '********'
      });
    });

    it('should handle Class B private IP range (**********/12)', () => {
      const result = parseCameraId('camera-warehouse-172-20-1-100');
      expect(result).toEqual({
        collectionName: 'Warehouse',
        ip: '************'
      });
    });

    it('should handle Class C private IP range (***********/16)', () => {
      const result = parseCameraId('camera-home-network-192-168-1-254');
      expect(result).toEqual({
        collectionName: 'Home Network',
        ip: '*************'
      });
    });

    it('should handle edge case IPs like localhost', () => {
      const result = parseCameraId('camera-localhost-127-0-0-1');
      expect(result).toEqual({
        collectionName: 'Localhost',
        ip: '127.0.0.1'
      });
    });
  });

  describe('normalizeCollectionName', () => {
    it('should normalize collection names correctly', () => {
      expect(normalizeCollectionName('Test Collection')).toBe('test-collection');
      expect(normalizeCollectionName('Test & Collection!')).toBe('test-collection');
      expect(normalizeCollectionName(' Spaces  ')).toBe('spaces');
    });
  });

  describe('denormalizeCollectionName', () => {
    it('should denormalize collection names correctly', () => {
      expect(denormalizeCollectionName('test-collection')).toBe('Test Collection');
      expect(denormalizeCollectionName('spaces')).toBe('Spaces');
    });
  });

  describe('normalizeCameraId', () => {
    it('should normalize camera IDs correctly', () => {
      expect(normalizeCameraId('Camera-123')).toBe('camera-123');
      expect(normalizeCameraId(' Camera 123! ')).toBe('camera123');
    });
  });

  describe('parseStreamUrl', () => {
    it('should parse valid RTSP URLs correctly', () => {
      const result = parseStreamUrl('rtsp://admin:pass@*************:554/stream1');
      expect(result).toEqual({
        collectionName: 'stream1',
        ip: '*************'
      });
    });

    it('should handle empty URLs gracefully', () => {
      const result = parseStreamUrl('');
      expect(result).toEqual({
        collectionName: '',
        ip: ''
      });
    });

    it('should handle invalid URLs gracefully', () => {
      const result = parseStreamUrl('not-a-url');
      expect(result).toEqual({
        collectionName: '',
        ip: ''
      });
    });
  });
});